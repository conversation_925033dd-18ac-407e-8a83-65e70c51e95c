-- 创建产品组合包表
-- 执行时间：2025-06-14

-- 创建产品组合包表
CREATE TABLE IF NOT EXISTS `product_bundles` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '组合包ID',
  `name` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '组合包名称',
  `description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '组合包描述（现在存储图片URL）',
  `discount` decimal(10, 2) NOT NULL DEFAULT 0.00 COMMENT '组合包优惠金额',
  `scenic_ids` json NOT NULL COMMENT '包含的景区ID数组（JSON格式）',
  `status` tinyint NOT NULL DEFAULT 1 COMMENT '状态：1-启用，0-禁用',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_status` (`status`) USING BTREE,
  INDEX `idx_created_at` (`created_at`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '产品组合包表' ROW_FORMAT = Dynamic;

-- 插入示例数据
INSERT IGNORE INTO `product_bundles` (`id`, `name`, `description`, `discount`, `scenic_ids`, `status`, `created_at`) VALUES
(1, '北京经典三日游套餐', 'https://example.com/bundle1.jpg', 50.00, '["scenic_001", "scenic_002", "scenic_003"]', 1, NOW()),
(2, '故宫深度体验包', 'https://example.com/bundle2.jpg', 30.00, '["scenic_001"]', 1, NOW()),
(3, '皇家园林精品游', 'https://example.com/bundle3.jpg', 40.00, '["scenic_002", "scenic_003"]', 1, NOW());

-- 提示信息
SELECT '产品组合包表创建完成！' AS message;
SELECT '表名：product_bundles' AS table_info;
SELECT '字段说明：scenic_ids 使用 JSON 类型存储景区ID数组' AS field_info;
SELECT '已插入3条示例数据' AS data_info;
