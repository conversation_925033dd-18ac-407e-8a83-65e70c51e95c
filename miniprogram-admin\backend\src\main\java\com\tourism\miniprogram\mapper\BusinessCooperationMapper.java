package com.tourism.miniprogram.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.tourism.miniprogram.entity.BusinessCooperation;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * 商务合作Mapper接口
 *
 * <AUTHOR> Team
 * @since 2025-01-06
 */
@Mapper
public interface BusinessCooperationMapper extends BaseMapper<BusinessCooperation> {

    /**
     * 根据用户ID获取合作申请列表
     *
     * @param userId 用户ID
     * @return 合作申请列表
     */
    @Select("SELECT * FROM business_cooperation WHERE user_id = #{userId} ORDER BY created_at DESC")
    List<BusinessCooperation> selectCooperationsByUserId(@Param("userId") Integer userId);

    /**
     * 根据状态获取合作申请列表
     *
     * @param status 状态
     * @return 合作申请列表
     */
    @Select("SELECT * FROM business_cooperation WHERE status = #{status} ORDER BY created_at DESC")
    List<BusinessCooperation> selectCooperationsByStatus(@Param("status") String status);

    /**
     * 根据合作类型获取合作申请列表
     *
     * @param cooperationType 合作类型
     * @return 合作申请列表
     */
    @Select("SELECT * FROM business_cooperation WHERE cooperation_type = #{cooperationType} ORDER BY created_at DESC")
    List<BusinessCooperation> selectCooperationsByType(@Param("cooperationType") String cooperationType);

    /**
     * 根据优先级获取合作申请列表
     *
     * @param priority 优先级
     * @return 合作申请列表
     */
    @Select("SELECT * FROM business_cooperation WHERE priority = #{priority} ORDER BY created_at DESC")
    List<BusinessCooperation> selectCooperationsByPriority(@Param("priority") String priority);

    /**
     * 获取待处理的合作申请数量
     *
     * @return 待处理数量
     */
    @Select("SELECT COUNT(*) FROM business_cooperation WHERE status = 'pending'")
    Integer countPendingCooperations();

    /**
     * 根据关键词搜索合作申请
     *
     * @param keyword 关键词
     * @return 合作申请列表
     */
    @Select("SELECT * FROM business_cooperation WHERE name LIKE CONCAT('%', #{keyword}, '%') " +
            "OR phone LIKE CONCAT('%', #{keyword}, '%') " +
            "OR company LIKE CONCAT('%', #{keyword}, '%') " +
            "OR content LIKE CONCAT('%', #{keyword}, '%') " +
            "ORDER BY created_at DESC")
    List<BusinessCooperation> searchCooperations(@Param("keyword") String keyword);
}
