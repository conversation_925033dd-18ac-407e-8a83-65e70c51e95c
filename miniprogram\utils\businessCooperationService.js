const request = require('./request');

class BusinessCooperationService {
  constructor() {
    this.baseUrl = '/business-cooperation';
  }

  /**
   * 创建商务合作申请
   * @param {Object} data 合作申请数据
   * @param {string} data.name 姓名
   * @param {string} data.phone 电话
   * @param {string} data.content 合作内容
   * @param {number} data.userId 用户ID
   */
  async createCooperation(data) {
    try {
      console.log('创建商务合作申请:', data);
      const response = await request.post(this.baseUrl, data);
      console.log('创建商务合作申请响应:', response);
      return response;
    } catch (error) {
      console.error('创建商务合作申请失败:', error);
      throw error;
    }
  }

  /**
   * 获取用户的商务合作申请列表
   * @param {number} userId 用户ID
   * @param {Object} params 查询参数
   */
  async getCooperationList(userId, params = {}) {
    try {
      const queryParams = {
        userId: userId,
        current: params.current || 1,
        size: params.size || 10,
        ...params
      };
      
      console.log('获取商务合作申请列表:', queryParams);
      const response = await request.get(`${this.baseUrl}/page`, queryParams);
      console.log('获取商务合作申请列表响应:', response);
      return response;
    } catch (error) {
      console.error('获取商务合作申请列表失败:', error);
      throw error;
    }
  }

  /**
   * 获取商务合作申请详情
   * @param {number} cooperationId 合作申请ID
   */
  async getCooperationDetail(cooperationId) {
    try {
      console.log('获取商务合作申请详情:', cooperationId);
      const response = await request.get(`${this.baseUrl}/${cooperationId}`);
      console.log('获取商务合作申请详情响应:', response);
      return response;
    } catch (error) {
      console.error('获取商务合作申请详情失败:', error);
      throw error;
    }
  }

  /**
   * 更新商务合作申请
   * @param {number} cooperationId 合作申请ID
   * @param {Object} data 更新数据
   */
  async updateCooperation(cooperationId, data) {
    try {
      console.log('更新商务合作申请:', cooperationId, data);
      const response = await request.put(`${this.baseUrl}/${cooperationId}`, data);
      console.log('更新商务合作申请响应:', response);
      return response;
    } catch (error) {
      console.error('更新商务合作申请失败:', error);
      throw error;
    }
  }

  /**
   * 删除商务合作申请
   * @param {number} cooperationId 合作申请ID
   */
  async deleteCooperation(cooperationId) {
    try {
      console.log('删除商务合作申请:', cooperationId);
      const response = await request.delete(`${this.baseUrl}/${cooperationId}`);
      console.log('删除商务合作申请响应:', response);
      return response;
    } catch (error) {
      console.error('删除商务合作申请失败:', error);
      throw error;
    }
  }

  /**
   * 搜索商务合作申请
   * @param {string} keyword 搜索关键词
   * @param {Object} params 查询参数
   */
  async searchCooperations(keyword, params = {}) {
    try {
      const queryParams = {
        keyword: keyword,
        current: params.current || 1,
        size: params.size || 10,
        ...params
      };
      
      console.log('搜索商务合作申请:', queryParams);
      const response = await request.get(`${this.baseUrl}/search`, queryParams);
      console.log('搜索商务合作申请响应:', response);
      return response;
    } catch (error) {
      console.error('搜索商务合作申请失败:', error);
      throw error;
    }
  }
}

module.exports = new BusinessCooperationService();
