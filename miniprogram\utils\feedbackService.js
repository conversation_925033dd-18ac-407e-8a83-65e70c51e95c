const request = require('./request');

class FeedbackService {
  constructor() {
    this.baseUrl = '/feedback';
  }

  /**
   * 创建意见反馈
   * @param {Object} data 反馈数据
   * @param {string} data.name 姓名
   * @param {string} data.phone 电话
   * @param {string} data.content 反馈内容
   * @param {number} data.userId 用户ID
   */
  async createFeedback(data) {
    try {
      console.log('创建意见反馈:', data);
      const response = await request.post(this.baseUrl, data);
      console.log('创建意见反馈响应:', response);
      return response;
    } catch (error) {
      console.error('创建意见反馈失败:', error);
      throw error;
    }
  }

  /**
   * 获取用户的意见反馈列表
   * @param {number} userId 用户ID
   * @param {Object} params 查询参数
   */
  async getFeedbackList(userId, params = {}) {
    try {
      const queryParams = {
        userId: userId,
        current: params.current || 1,
        size: params.size || 10,
        ...params
      };
      
      console.log('获取意见反馈列表:', queryParams);
      const response = await request.get(`${this.baseUrl}/page`, queryParams);
      console.log('获取意见反馈列表响应:', response);
      return response;
    } catch (error) {
      console.error('获取意见反馈列表失败:', error);
      throw error;
    }
  }

  /**
   * 获取意见反馈详情
   * @param {number} feedbackId 反馈ID
   */
  async getFeedbackDetail(feedbackId) {
    try {
      console.log('获取意见反馈详情:', feedbackId);
      const response = await request.get(`${this.baseUrl}/${feedbackId}`);
      console.log('获取意见反馈详情响应:', response);
      return response;
    } catch (error) {
      console.error('获取意见反馈详情失败:', error);
      throw error;
    }
  }

  /**
   * 更新意见反馈
   * @param {number} feedbackId 反馈ID
   * @param {Object} data 更新数据
   */
  async updateFeedback(feedbackId, data) {
    try {
      console.log('更新意见反馈:', feedbackId, data);
      const response = await request.put(`${this.baseUrl}/${feedbackId}`, data);
      console.log('更新意见反馈响应:', response);
      return response;
    } catch (error) {
      console.error('更新意见反馈失败:', error);
      throw error;
    }
  }

  /**
   * 删除意见反馈
   * @param {number} feedbackId 反馈ID
   */
  async deleteFeedback(feedbackId) {
    try {
      console.log('删除意见反馈:', feedbackId);
      const response = await request.delete(`${this.baseUrl}/${feedbackId}`);
      console.log('删除意见反馈响应:', response);
      return response;
    } catch (error) {
      console.error('删除意见反馈失败:', error);
      throw error;
    }
  }
}

module.exports = new FeedbackService();
