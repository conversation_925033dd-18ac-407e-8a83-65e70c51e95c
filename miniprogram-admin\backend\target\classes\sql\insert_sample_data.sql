-- 插入示例数据用于测试产品管理功能
-- 执行时间：2025-01-11

-- 插入示例用户数据（如果不存在）
INSERT IGNORE INTO `user` (`id`, `openid`, `nickname`, `avatar_url`, `phone`, `created_at`) VALUES
(1, 'test_openid_001', '测试用户1', 'https://example.com/avatar1.jpg', '13800138001', NOW()),
(2, 'test_openid_002', '测试用户2', 'https://example.com/avatar2.jpg', '13800138002', NOW()),
(3, 'test_openid_003', '测试用户3', 'https://example.com/avatar3.jpg', '13800138003', NOW());

-- 插入示例景区数据（如果不存在）
INSERT IGNORE INTO `scenics` (`id`, `name`, `description`, `province_id`, `city_id`, `address`, `latitude`, `longitude`, `phone`, `price`, `rating`, `album`, `sort`, `status`, `created_at`) VALUES
(1, '故宫博物院', '中国明清两代的皇家宫殿', 1, 1, '北京市东城区景山前街4号', 39.9163, 116.3972, '010-85007421', 60.00, 4.8, '["https://example.com/palace1.jpg","https://example.com/palace2.jpg"]', 1, 1, NOW()),
(2, '天坛公园', '明清两朝皇帝祭天的场所', 1, 1, '北京市东城区天坛路甲1号', 39.8828, 116.4067, '010-67028866', 35.00, 4.6, '["https://example.com/temple1.jpg","https://example.com/temple2.jpg"]', 2, 1, NOW()),
(3, '颐和园', '中国古典园林之首', 1, 1, '北京市海淀区新建宫门路19号', 39.9998, 116.2754, '010-62881144', 30.00, 4.7, '["https://example.com/garden1.jpg","https://example.com/garden2.jpg"]', 3, 1, NOW());

-- 插入示例订单数据
INSERT IGNORE INTO `orders` (`id`, `user_id`, `order_no`, `total_amount`, `status`, `created_at`) VALUES
(1, 1, 'ORD202501110001', 120.00, 'paid', NOW()),
(2, 2, 'ORD202501110002', 95.00, 'paid', NOW()),
(3, 3, 'ORD202501110003', 180.00, 'pending', NOW()),
(4, 1, 'ORD202501110004', 60.00, 'paid', NOW()),
(5, 2, 'ORD202501110005', 150.00, 'canceled', NOW());

-- 插入示例产品数据
INSERT IGNORE INTO `products` (`id`, `scenic_id`, `name`, `description`, `price`, `validity_hours`, `require_activation`, `product_type`, `status`, `order_id`, `created_at`) VALUES
(1, 1, '故宫深度游讲解', '专业导游带您深度了解故宫历史文化', 60.00, 24, 1, 'single', 1, 1, NOW()),
(2, 1, '故宫VIP套餐', '包含讲解、纪念品和茶歇的豪华套餐', 120.00, 48, 1, 'bundle', 1, 1, NOW()),
(3, 2, '天坛祭天文化讲解', '了解古代祭天仪式和建筑奥秘', 35.00, 12, 0, 'single', 1, 2, NOW()),
(4, 2, '天坛文化体验套餐', '讲解+文化体验+纪念品', 60.00, 24, 1, 'bundle', 1, 2, NOW()),
(5, 3, '颐和园皇家园林讲解', '探索中国古典园林艺术精髓', 30.00, 24, 0, 'single', 1, 4, NOW()),
(6, 3, '颐和园全景体验', '讲解+游船+茶艺体验', 90.00, 48, 1, 'bundle', 1, 3, NOW());

-- 提示信息
SELECT '示例数据插入完成！' AS message;
SELECT '已插入：3个用户、3个景区、5个订单、6个产品' AS data_info;
