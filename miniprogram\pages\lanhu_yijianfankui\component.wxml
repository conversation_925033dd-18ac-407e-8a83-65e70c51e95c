<view class="page">
  <!-- 反馈列表 -->
  <view wx:if="{{feedbackList.length > 0}}" class="feedback-list">
    <view wx:for="{{feedbackList}}" wx:key="id" class="feedback-item">
      <view class="feedback-header">
        <text class="feedback-status status-{{item.status}}">{{getStatusText(item.status)}}</text>
        <text class="feedback-time">{{formatTime(item.createdAt)}}</text>
      </view>
      <view class="feedback-content">
        <text class="content-text">{{item.content}}</text>
      </view>
      <view wx:if="{{item.reply}}" class="feedback-reply">
        <text class="reply-label">回复：</text>
        <text class="reply-text">{{item.reply}}</text>
      </view>
    </view>
  </view>

  <!-- 空状态 -->
  <view wx:else class="box_2">
    <view class="text-wrapper_1">
      <text lines="1" class="text_2">您还没有提过意见哦~</text>
    </view>
    <view class="group_1" bindtap="onCreateFeedback">
      <view class="image-text_1">
        <image src="../../images/lanhu_yijianfankui/FigmaDDSSlicePNG69b50bdbdc4fa25e5c97fe2810edbe9b.png" class="thumbnail_4"></image>
        <text lines="1" class="text-group_1">填写意见</text>
      </view>
    </view>
    <view class="group_2"></view>
  </view>

  <!-- 浮动添加按钮 -->
  <view class="floating-add-btn" bindtap="onCreateFeedback">
    <text class="add-btn-text">+</text>
  </view>

  <!-- 创建反馈弹窗 -->
  <view wx:if="{{showCreateModal}}" class="modal-overlay" bindtap="onCloseModal">
    <view class="modal-content" catchtap="stopPropagation">
      <view class="modal-header">
        <text class="modal-title">填写意见反馈</text>
        <text class="modal-close" bindtap="onCloseModal">×</text>
      </view>
      <view class="modal-body">
        <view class="form-item">
          <text class="form-label">姓名</text>
          <input class="form-input" placeholder="请输入您的姓名" value="{{formData.name}}" bindinput="onNameInput" />
        </view>
        <view class="form-item">
          <text class="form-label">电话</text>
          <input class="form-input" placeholder="请输入您的电话" value="{{formData.phone}}" bindinput="onPhoneInput" type="number" />
        </view>
        <view class="form-item">
          <text class="form-label">反馈内容</text>
          <textarea class="form-textarea" placeholder="请输入您的意见或建议" value="{{formData.content}}" bindinput="onContentInput" maxlength="500"></textarea>
        </view>
      </view>
      <view class="modal-footer">
        <button class="btn-cancel" bindtap="onCloseModal">取消</button>
        <button class="btn-submit" bindtap="onSubmitFeedback" disabled="{{isSubmitting}}">
          {{isSubmitting ? '提交中...' : '提交'}}
        </button>
      </view>
    </view>
  </view>
</view>