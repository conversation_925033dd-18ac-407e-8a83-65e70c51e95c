package com.tourism.miniprogram.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.tourism.miniprogram.entity.BusinessCooperation;

import java.util.List;

/**
 * 商务合作服务接口
 *
 * <AUTHOR> Team
 * @since 2025-01-06
 */
public interface BusinessCooperationService extends IService<BusinessCooperation> {

    /**
     * 根据用户ID获取合作申请列表
     *
     * @param userId 用户ID
     * @return 合作申请列表
     */
    List<BusinessCooperation> getCooperationsByUserId(Integer userId);

    /**
     * 根据状态获取合作申请列表
     *
     * @param status 状态
     * @return 合作申请列表
     */
    List<BusinessCooperation> getCooperationsByStatus(String status);

    /**
     * 根据合作类型获取合作申请列表
     *
     * @param cooperationType 合作类型
     * @return 合作申请列表
     */
    List<BusinessCooperation> getCooperationsByType(String cooperationType);

    /**
     * 根据优先级获取合作申请列表
     *
     * @param priority 优先级
     * @return 合作申请列表
     */
    List<BusinessCooperation> getCooperationsByPriority(String priority);

    /**
     * 获取待处理的合作申请数量
     *
     * @return 待处理数量
     */
    Integer getPendingCooperationCount();

    /**
     * 根据关键词搜索合作申请
     *
     * @param keyword 关键词
     * @return 合作申请列表
     */
    List<BusinessCooperation> searchCooperations(String keyword);

    /**
     * 更新合作申请状态
     *
     * @param cooperationId 合作申请ID
     * @param status 新状态
     * @param contactUserId 联系人用户ID
     * @return 是否成功
     */
    boolean updateCooperationStatus(Integer cooperationId, String status, Integer contactUserId);

    /**
     * 更新合作申请优先级
     *
     * @param cooperationId 合作申请ID
     * @param priority 新优先级
     * @return 是否成功
     */
    boolean updateCooperationPriority(Integer cooperationId, String priority);

    /**
     * 添加备注信息
     *
     * @param cooperationId 合作申请ID
     * @param notes 备注信息
     * @return 是否成功
     */
    boolean addNotes(Integer cooperationId, String notes);

    /**
     * 批量更新合作申请状态
     *
     * @param cooperationIds 合作申请ID列表
     * @param status 新状态
     * @return 是否成功
     */
    boolean batchUpdateCooperationStatus(List<Integer> cooperationIds, String status);
}
