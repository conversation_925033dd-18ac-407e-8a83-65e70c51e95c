package com.tourism.miniprogram.service.impl;

import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.tourism.miniprogram.entity.BusinessCooperation;
import com.tourism.miniprogram.mapper.BusinessCooperationMapper;
import com.tourism.miniprogram.service.BusinessCooperationService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 商务合作服务实现类
 *
 * <AUTHOR> Team
 * @since 2025-01-06
 */
@Slf4j
@Service
public class BusinessCooperationServiceImpl extends ServiceImpl<BusinessCooperationMapper, BusinessCooperation> implements BusinessCooperationService {

    @Override
    public List<BusinessCooperation> getCooperationsByUserId(Integer userId) {
        try {
            return baseMapper.selectCooperationsByUserId(userId);
        } catch (Exception e) {
            log.error("根据用户ID获取合作申请列表失败，userId: {}", userId, e);
            throw new RuntimeException("获取合作申请列表失败");
        }
    }

    @Override
    public List<BusinessCooperation> getCooperationsByStatus(String status) {
        try {
            return baseMapper.selectCooperationsByStatus(status);
        } catch (Exception e) {
            log.error("根据状态获取合作申请列表失败，status: {}", status, e);
            throw new RuntimeException("获取合作申请列表失败");
        }
    }

    @Override
    public List<BusinessCooperation> getCooperationsByType(String cooperationType) {
        try {
            return baseMapper.selectCooperationsByType(cooperationType);
        } catch (Exception e) {
            log.error("根据合作类型获取合作申请列表失败，cooperationType: {}", cooperationType, e);
            throw new RuntimeException("获取合作申请列表失败");
        }
    }

    @Override
    public List<BusinessCooperation> getCooperationsByPriority(String priority) {
        try {
            return baseMapper.selectCooperationsByPriority(priority);
        } catch (Exception e) {
            log.error("根据优先级获取合作申请列表失败，priority: {}", priority, e);
            throw new RuntimeException("获取合作申请列表失败");
        }
    }

    @Override
    public Integer getPendingCooperationCount() {
        try {
            return baseMapper.countPendingCooperations();
        } catch (Exception e) {
            log.error("获取待处理合作申请数量失败", e);
            throw new RuntimeException("获取待处理合作申请数量失败");
        }
    }

    @Override
    public List<BusinessCooperation> searchCooperations(String keyword) {
        try {
            return baseMapper.searchCooperations(keyword);
        } catch (Exception e) {
            log.error("搜索合作申请失败，keyword: {}", keyword, e);
            throw new RuntimeException("搜索合作申请失败");
        }
    }

    @Override
    @Transactional
    public boolean updateCooperationStatus(Integer cooperationId, String status, Integer contactUserId) {
        try {
            UpdateWrapper<BusinessCooperation> updateWrapper = new UpdateWrapper<>();
            updateWrapper.eq("id", cooperationId)
                    .set("status", status)
                    .set("contact_time", LocalDateTime.now())
                    .set("contact_user_id", contactUserId);

            boolean success = update(updateWrapper);
            if (success) {
                log.info("更新合作申请状态成功，cooperationId: {}, status: {}, contactUserId: {}", 
                        cooperationId, status, contactUserId);
            } else {
                log.error("更新合作申请状态失败，cooperationId: {}", cooperationId);
            }
            return success;
        } catch (Exception e) {
            log.error("更新合作申请状态失败，cooperationId: {}, status: {}, contactUserId: {}", 
                    cooperationId, status, contactUserId, e);
            throw new RuntimeException("更新合作申请状态失败");
        }
    }

    @Override
    @Transactional
    public boolean updateCooperationPriority(Integer cooperationId, String priority) {
        try {
            UpdateWrapper<BusinessCooperation> updateWrapper = new UpdateWrapper<>();
            updateWrapper.eq("id", cooperationId).set("priority", priority);

            boolean success = update(updateWrapper);
            if (success) {
                log.info("更新合作申请优先级成功，cooperationId: {}, priority: {}", cooperationId, priority);
            } else {
                log.error("更新合作申请优先级失败，cooperationId: {}", cooperationId);
            }
            return success;
        } catch (Exception e) {
            log.error("更新合作申请优先级失败，cooperationId: {}, priority: {}", cooperationId, priority, e);
            throw new RuntimeException("更新合作申请优先级失败");
        }
    }

    @Override
    @Transactional
    public boolean addNotes(Integer cooperationId, String notes) {
        try {
            UpdateWrapper<BusinessCooperation> updateWrapper = new UpdateWrapper<>();
            updateWrapper.eq("id", cooperationId).set("notes", notes);

            boolean success = update(updateWrapper);
            if (success) {
                log.info("添加合作申请备注成功，cooperationId: {}", cooperationId);
            } else {
                log.error("添加合作申请备注失败，cooperationId: {}", cooperationId);
            }
            return success;
        } catch (Exception e) {
            log.error("添加合作申请备注失败，cooperationId: {}", cooperationId, e);
            throw new RuntimeException("添加合作申请备注失败");
        }
    }

    @Override
    @Transactional
    public boolean batchUpdateCooperationStatus(List<Integer> cooperationIds, String status) {
        try {
            UpdateWrapper<BusinessCooperation> updateWrapper = new UpdateWrapper<>();
            updateWrapper.in("id", cooperationIds).set("status", status);

            boolean success = update(updateWrapper);
            if (success) {
                log.info("批量更新合作申请状态成功，cooperationIds: {}, status: {}", cooperationIds, status);
            } else {
                log.error("批量更新合作申请状态失败，cooperationIds: {}", cooperationIds);
            }
            return success;
        } catch (Exception e) {
            log.error("批量更新合作申请状态失败，cooperationIds: {}, status: {}", cooperationIds, status, e);
            throw new RuntimeException("批量更新合作申请状态失败");
        }
    }
}
