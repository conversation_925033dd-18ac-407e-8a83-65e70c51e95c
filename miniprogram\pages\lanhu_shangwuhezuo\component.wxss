.page {
  background-color: rgba(245,245,249,1.000000);
  position: relative;
  width: 750rpx;
  height: 1628rpx;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}
.group_1 {
  background-color: rgba(255,255,255,1.000000);
  height: 176rpx;
  width: 750rpx;
  display: flex;
  flex-direction: column;
}
.image-wrapper_1 {
  width: 656rpx;
  height: 24rpx;
  flex-direction: row;
  display: flex;
  margin: 36rpx 0 0 48rpx;
}
.image_1 {
  width: 54rpx;
  height: 22rpx;
}
.thumbnail_1 {
  width: 34rpx;
  height: 22rpx;
  margin-left: 468rpx;
}
.thumbnail_2 {
  width: 32rpx;
  height: 24rpx;
  margin-left: 10rpx;
}
.image_2 {
  width: 48rpx;
  height: 22rpx;
  margin-left: 10rpx;
}
.group_2 {
  width: 402rpx;
  height: 46rpx;
  flex-direction: row;
  display: flex;
  justify-content: space-between;
  margin: 48rpx 0 22rpx 38rpx;
}
.thumbnail_3 {
  width: 34rpx;
  height: 34rpx;
  margin-top: 12rpx;
}
.text_1 {
  width: 128rpx;
  height: 44rpx;
  overflow-wrap: break-word;
  color: rgba(0,0,0,0.900000);
  font-size: 32rpx;
  font-family: PingFang SC-Medium;
  font-weight: 500;
  text-align: center;
  white-space: nowrap;
  line-height: 32rpx;
}
.group_3 {
  width: 750rpx;
  height: 1454rpx;
  margin-bottom: 2rpx;
  display: flex;
  flex-direction: column;
}
.text-wrapper_1 {
  background-color: rgba(255,255,255,1.000000);
  height: 110rpx;
  margin-top: 20rpx;
  display: flex;
  flex-direction: column;
  width: 750rpx;
}
.text_2 {
  width: 140rpx;
  height: 40rpx;
  overflow-wrap: break-word;
  color: rgba(0,0,0,0.400000);
  font-size: 28rpx;
  font-family: PingFang SC-Regular;
  font-weight: normal;
  text-align: left;
  white-space: nowrap;
  line-height: 28rpx;
  margin: 34rpx 0 0 30rpx;
}
.text-wrapper_2 {
  background-color: rgba(255,255,255,1.000000);
  height: 110rpx;
  margin-top: 2rpx;
  display: flex;
  flex-direction: column;
  width: 750rpx;
}
.text_3 {
  width: 168rpx;
  height: 40rpx;
  overflow-wrap: break-word;
  color: rgba(0,0,0,0.400000);
  font-size: 28rpx;
  font-family: PingFang SC-Regular;
  font-weight: normal;
  text-align: left;
  white-space: nowrap;
  line-height: 28rpx;
  margin: 34rpx 0 0 30rpx;
}
.text-wrapper_3 {
  background-color: rgba(255,255,255,1.000000);
  height: 242rpx;
  margin-top: 20rpx;
  display: flex;
  flex-direction: column;
  width: 750rpx;
}
.text_4 {
  width: 196rpx;
  height: 40rpx;
  overflow-wrap: break-word;
  color: rgba(0,0,0,0.400000);
  font-size: 28rpx;
  font-family: PingFang SC-Regular;
  font-weight: normal;
  text-align: left;
  white-space: nowrap;
  line-height: 28rpx;
  margin: 34rpx 0 0 30rpx;
}
.text-wrapper_4 {
  background-color: rgba(64,128,255,1.000000);
  border-radius: 200rpx;
  height: 100rpx;
  display: flex;
  flex-direction: column;
  width: 690rpx;
  margin: 726rpx 0 0 30rpx;
}
.text_5 {
  width: 128rpx;
  height: 34rpx;
  overflow-wrap: break-word;
  color: rgba(255,255,255,1.000000);
  font-size: 32rpx;
  font-family: PingFang SC-Medium;
  font-weight: 500;
  text-align: left;
  white-space: nowrap;
  line-height: 34rpx;
  margin: 34rpx 0 0 282rpx;
}
.box_1 {
  background-color: rgba(0,0,0,1.000000);
  border-radius: 8rpx;
  width: 284rpx;
  height: 8rpx;
  display: flex;
  flex-direction: column;
  margin: 86rpx 0 30rpx 232rpx;
}