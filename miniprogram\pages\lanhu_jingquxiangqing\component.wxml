<view class="page" style="background-color: {{productDetail.backgroundColor || 'rgba(245, 245, 249, 1.000000)'}};">
  <!-- 加载状态 -->
  <view wx:if="{{loading}}" class="loading-container">
    <text class="loading-text">加载中...</text>
  </view>

  <!-- 错误状态 -->
  <view wx:elif="{{error}}" class="error-container">
    <text class="error-text">{{errorMessage}}</text>
    <button bindtap="onRetryLoad" class="retry-btn">重试</button>
  </view>

  <!-- 产品详情内容 -->
  <view wx:else class="content-container">
    <view class="block_2">
      <view class="block_3">
        <view class="image-wrapper_2">
          <image src="{{lecturerDetail.avatarUrl || '../../images/lanhu_jingquxiangqing/FigmaDDSSlicePNG5a54f9d203246edd37c47deebf23e81e.png'}}" class="image_3"></image>
        </view>
        <view class="box_1">
          <view class="group_2">
            <text lines="1" class="text_2">{{lecturerDetail.name || '专业讲解员'}}</text>
          </view>
          <text lines="2" class="paragraph_1">{{lecturerDetail.intro || '暂无描述'}}</text>
        </view>
      </view>
      <view class="text-wrapper_2">
        <text lines="1" class="text_4">景区地图</text>
      </view>
    </view>
    <image src="{{productDetail.mapUrl || ''}}" class="image_4"></image>
    <!-- 指引图 -->
    <image src="{{productDetail.startListeningImageUrl || ''}}" class="block_4"></image>
    <view class="block_6">
      <view class="text-wrapper_5" style="position: relative; left: 0rpx; top: -2rpx">
        <text lines="1" class="text_7">{{productDetail.title || ''}}</text>
      </view>
      <!-- 区域标签区域（横向滑动布局） -->
      <view wx:if="{{areaList.length > 0}}" class="area-tags-container">
        <scroll-view class="area-tags-scroll" scroll-x="true" show-scrollbar="false">
          <view class="area-tags-wrapper">
            <view wx:for="{{areaList}}" wx:key="areaId" class="area-tag-item {{selectedArea && selectedArea.areaId === item.areaId ? 'text-wrapper_6' : 'text-wrapper_7'}}" bindtap="onAreaTagClick" data-areaid="{{item.areaId}}" data-areaname="{{item.areaName}}">
              <text lines="1" class="{{selectedArea && selectedArea.areaId === item.areaId ? 'text_8' : 'text_9'}}">{{item.areaName}}</text>
            </view>
          </view>
        </scroll-view>
      </view>
      <view wx:if="{{selectedArea}}" class="text-wrapper_8">
        <text lines="1" class="text_10">{{selectedArea.areaName}}</text>
      </view>
    </view>

    <!-- 讲解点列表区域 -->
    <view wx:if="{{pointDataList && pointDataList.length > 0}}" >
      <!-- 遍历讲解点 -->
      <view wx:for="{{pointDataList}}" wx:key="pointId" wx:for-item="pointItem" wx:for-index="pointIndex" class="block_12" style="height: {{pointItem.audioList.length === 1 ? 1000 : pointItem.audioList.length === 2 ? 1300 : pointItem.audioList.length === 3 ? 1600 : pointItem.audioList.length === 4 ? 1900 : pointItem.audioList.length === 5 ? 2200 : pointItem.audioList.length === 6 ? 2500 : pointItem.audioList.length === 7 ? 2800 : pointItem.audioList.length === 8 ? 3100 : pointItem.audioList.length === 9 ? 3400 : pointItem.audioList.length === 10 ? 3700 : pointItem.audioList.length === 11 ? 4000 : 'auto'}}rpx">
        <!-- 讲解点图片 -->
        <image class="block_image_12" src="{{pointItem.pointImage || selectedArea.pointImage}}"></image>
        <!-- 该讲解点下的音频列表 -->
        <view class="box_13">
          <view wx:for="{{pointItem.audioList}}" wx:key="id" wx:for-item="audioItem" wx:for-index="audioIndex" class="box_13_1">
            <view class="section_6">
              <view class="text-wrapper_23">
                <text lines="1" class="text_25">{{audioItem.title}}</text>
                <text lines="1" class="text_26">时长：{{audioItem.duration}}</text>
                <text lines="1" class="text_27">位置：{{audioItem.location}}</text>
              </view>
              <image src="{{audioItem.descriptionImageUrl}}" class="image_9"></image>
            </view>
            <view class="section_7">
              <image src="{{audioItem.isPlaying ? '/images/lanhu_jingquxiangqing/bofang.png' : '/images/lanhu_jingquxiangqing/bofang.png'}}" class="thumbnail_6 {{audioItem.isPlaying ? 'playing' : ''}}" bindtap="onAudioPlay" data-audiourl="{{audioItem.audioUrl}}" data-audiotitle="{{audioItem.title}}" data-audioid="{{audioItem.id}}" data-pointindex="{{pointIndex}}" data-audioindex="{{audioIndex}}"></image>
              <view class="block_13">
                <view class="group_8" style="width: {{audioItem.progress || 0}}%"></view>
              </view>
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 顶部背景图和视频 -->
    <view class="block_11" style="position: absolute; left: 0rpx; top: 32rpx">
      <!-- 顶部背景图 -->
      <image class="block_11" src="{{productDetail.backgroundImageUrl || ''}}" style="position: absolute; left: 0rpx; top: 0rpx"></image>
      <view class="group_6">
        <!-- 视频 -->
        <video src="{{productDetail.exampleVideoUrl || ''}}" class="box_9"></video>
      </view>
    </view>
  </view>

  <!-- 快捷置顶 -->
  <view wx:if="{{showBackToTop}}" class="back-to-top-btn" bindtap="onBackToTop">
    <view class="image-wrapper_5">
      <image src="../../images/lanhu_jingquxiangqing/FigmaDDSSlicePNGb8f94fd6afb41d1595fb5264b9eead56.png" class="thumbnail_4"></image>
    </view>
  </view>

  <!-- 立即购买 - 固定在底部，水平居中对齐 -->
  <view class="fixed-buy-section">
    <view class="text-wrapper_18" bindtap="onBuyNow">
      <text lines="1" class="text_29">立即购买</text>
      <text lines="1" class="text_30">￥{{productDetail.price || 0}}</text>
    </view>
  </view>

</view>