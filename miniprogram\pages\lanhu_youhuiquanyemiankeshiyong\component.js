const apiService = require('../../utils/apiService');
const userService = require('../../utils/userService');

Page({
  data: {
    userId: null,
    coupons: [],
    loading: false,
    activeTab: 'all', // all, unused, used
    tabs: [
      { key: 'all', name: '全部', status: null, count: 0 },
      { key: 'unused', name: '未使用', status: 'unactivated', count: 0 },
      { key: 'used', name: '已使用', status: 'used', count: 0 }
    ],
    allCoupons: [] // 存储所有门票数据用于筛选
  },
  // 页面生命周期
  onLoad: function (options) {
    console.info("门票列表页面加载");
    // 接收页面参数
    if (options.status) {
      this.setData({
        activeTab: options.status
      });
    }
    if (options.userId) {
      this.setData({
        userId: parseInt(options.userId)
      });
    }
    this.initPage();
  },

  onShow: function () {
    // 页面显示时刷新数据
    if (this.data.userId) {
      this.loadCoupons(true);
    }
  },

  onUnload: function () {
    console.info("门票列表页面卸载");
  },

  // 下拉刷新
  onPullDownRefresh: function () {
    this.loadCoupons(true).finally(() => {
      wx.stopPullDownRefresh();
    });
  },

  // 移除上拉加载更多逻辑，因为直接加载全部数据
    // 初始化页面
    async initPage() {
      try {
        // 获取当前用户信息
        let userInfo;
        try {
          // 先尝试从本地存储获取用户信息
          const storedUserInfo = wx.getStorageSync('userInfo');
          if (storedUserInfo && storedUserInfo.id) {
            userInfo = storedUserInfo;
          } else {
            // 如果没有本地用户信息，使用默认用户ID
            userInfo = { id: 1, name: '用户' };
          }
        } catch (error) {
          console.error('获取用户信息失败:', error);
          // 使用默认用户ID
          userInfo = { id: 1, name: '用户' };
        }

        if (!userInfo || !userInfo.id) {
          wx.showToast({
            title: '请先登录',
            icon: 'none'
          });
          return;
        }

        this.setData({
          userId: userInfo.id
        });

        // 加载门票列表
        await this.loadCoupons(true);
      } catch (error) {
        console.error('初始化页面失败:', error);
        wx.showToast({
          title: '页面加载失败',
          icon: 'none'
        });
      }
    },

    // 加载门票列表 - 直接加载全部数据
    async loadCoupons(reset = false) {
      if (this.data.loading) return;

      try {
        this.setData({ loading: true });

        if (reset) {
          this.setData({
            coupons: []
          });
        }

        // 获取用户的所有卡券，使用大的分页参数来获取全部数据
        console.log('开始获取用户卡券，用户ID:', this.data.userId);
        const response = await apiService.getUserCoupons(this.data.userId, {
          current: 1,
          size: 1000 // 使用大的size来获取所有数据
        });

        console.log('卡券API响应:', response);
        if (response && response.records && response.records.length > 0) {
          // 为每张卡券获取产品和景区详情
          const enrichedCoupons = await this.enrichCouponsWithDetails(response.records);

          // 存储所有门票数据
          this.setData({
            allCoupons: enrichedCoupons
          });

          // 更新标签计数
          this.updateTabCounts(enrichedCoupons);

          // 根据当前选中的tab过滤门票
          this.filterCoupons();
        } else {
          console.log('没有获取到卡券数据或数据为空');
          this.setData({
            allCoupons: [],
            coupons: []
          });
          this.updateTabCounts([]);
        }
      } catch (error) {
        console.error('加载门票列表失败:', error);
        wx.showToast({
          title: '加载失败',
          icon: 'none'
        });
      } finally {
        this.setData({ loading: false });
      }
    },

    // 为卡券获取产品和景区详情
    async enrichCouponsWithDetails(coupons) {
      const enrichedCoupons = [];

      for (const coupon of coupons) {
        try {
          const enrichedCoupon = { ...coupon };

          // 根据productId获取产品详情
          if (coupon.productId) {
            try {
              const productDetail = await apiService.getProductDetail(coupon.productId);
              enrichedCoupon.productDetail = productDetail;
              console.log(`获取产品详情成功 (产品ID: ${coupon.productId}):`, productDetail);

              // 根据产品的景区ID获取景区详情
              if (productDetail && productDetail.scenicId) {
                try {
                  const scenicDetail = await apiService.getScenicDetail(productDetail.scenicId);

                  // 获取省份和城市名称
                  if (scenicDetail && scenicDetail.provinceId && scenicDetail.cityId) {
                    try {
                      // 并行获取省份和城市信息以提高性能
                      const [provinceDetail, cityDetail] = await Promise.all([
                        apiService.getProvinceById(scenicDetail.provinceId).catch(err => {
                          console.error(`获取省份信息失败 (省份ID: ${scenicDetail.provinceId}):`, err);
                          return null;
                        }),
                        apiService.getCityById(scenicDetail.cityId).catch(err => {
                          console.error(`获取城市信息失败 (城市ID: ${scenicDetail.cityId}):`, err);
                          return null;
                        })
                      ]);

                      scenicDetail.provinceName = provinceDetail ? provinceDetail.name : '';
                      scenicDetail.cityName = cityDetail ? cityDetail.name : '';
                    } catch (locationError) {
                      console.error('获取省份城市信息失败:', locationError);
                      scenicDetail.provinceName = '';
                      scenicDetail.cityName = '';
                    }
                  }

                  enrichedCoupon.scenicDetail = scenicDetail;
                  console.log(`获取景区详情成功 (景区ID: ${productDetail.scenicId}):`, scenicDetail);
                } catch (scenicError) {
                  console.error(`获取景区详情失败 (景区ID: ${productDetail.scenicId}):`, scenicError);
                  enrichedCoupon.scenicDetail = null;
                }
              }
            } catch (productError) {
              console.error(`获取产品详情失败 (产品ID: ${coupon.productId}):`, productError);
              enrichedCoupon.productDetail = null;
              enrichedCoupon.scenicDetail = null;
            }
          }

          enrichedCoupons.push(enrichedCoupon);
        } catch (error) {
          console.error('处理卡券详情失败:', error);
          // 即使获取详情失败，也保留原始卡券数据
          enrichedCoupons.push({ ...coupon });
        }
      }

      return enrichedCoupons;
    },

    // 更新标签计数
    updateTabCounts(allCoupons) {
      const tabs = this.data.tabs.map(tab => {
        if (tab.key === 'all') {
          tab.count = allCoupons.length;
        } else if (tab.key === 'unused') {
          tab.count = allCoupons.filter(coupon =>
            coupon.status === 'unactivated' || coupon.status === 'active'
          ).length;
        } else if (tab.key === 'used') {
          tab.count = allCoupons.filter(coupon =>
            coupon.status === 'used' || coupon.status === 'expired'
          ).length;
        }
        return tab;
      });

      this.setData({ tabs });
    },

    // 根据当前tab过滤门票
    filterCoupons() {
      const { allCoupons, activeTab } = this.data;
      let filteredCoupons = allCoupons;

      if (activeTab === 'unused') {
        filteredCoupons = allCoupons.filter(coupon =>
          coupon.status === 'unactivated' || coupon.status === 'active'
        );
      } else if (activeTab === 'used') {
        filteredCoupons = allCoupons.filter(coupon =>
          coupon.status === 'used' || coupon.status === 'expired'
        );
      }

      // 按创建时间倒序排列并预处理数据
      filteredCoupons.sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt));

      // 预处理数据，添加格式化的字段
      const processedCoupons = filteredCoupons.map(coupon => {
        const processed = {
          ...coupon,
          formattedCreatedAt: this.formatDate(coupon.createdAt),
          formattedValidFrom: this.formatDate(coupon.validFrom),
          formattedValidTo: this.formatDate(coupon.validTo),
          formattedUsedAt: this.formatDate(coupon.usedAt),
          statusText: this.getStatusText(coupon.status),
          statusClass: this.getStatusClass(coupon.status),
          // 添加产品和景区信息的显示字段
          productName: coupon.productDetail ? coupon.productDetail.name : '未知产品',
          productPrice: coupon.productDetail ? coupon.productDetail.price : 0,
          scenicName: coupon.scenicDetail ? coupon.scenicDetail.title : '未知景区',
          scenicImage: coupon.scenicDetail ? coupon.scenicDetail.image : '',
          scenicLocation: coupon.scenicDetail ? `${coupon.scenicDetail.provinceName || ''}${coupon.scenicDetail.cityName || ''}` : '',
          scenicAddress: coupon.scenicDetail ? coupon.scenicDetail.address : ''
        };

        // 添加调试日志
        console.log(`卡券 ${coupon.id} 处理结果:`, {
          productDetail: coupon.productDetail,
          scenicDetail: coupon.scenicDetail,
          productName: processed.productName,
          scenicName: processed.scenicName,
          scenicLocation: processed.scenicLocation
        });

        return processed;
      });

      this.setData({
        coupons: processedCoupons
      });
    },

    // 切换标签
    onTabChange(e) {
      const tabKey = e.currentTarget.dataset.tab;
      if (tabKey === this.data.activeTab) return;

      this.setData({
        activeTab: tabKey
      });

      this.filterCoupons();
    },

    // 格式化日期
    formatDate(dateString) {
      if (!dateString) return '';
      const date = new Date(dateString);
      return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')}`;
    },

    // 获取门票状态文本
    getStatusText(status) {
      const statusMap = {
        'unactivated': '未激活',
        'active': '已激活',
        'used': '已使用',
        'expired': '已过期'
      };
      return statusMap[status] || status;
    },

    // 获取门票状态样式类
    getStatusClass(status) {
      const classMap = {
        'unactivated': 'status-unactivated',
        'active': 'status-active',
        'used': 'status-used',
        'expired': 'status-expired'
      };
      return classMap[status] || '';
    },

    // 使用门票
    async useCoupon(e) {
      const couponId = e.currentTarget.dataset.couponId;
      const coupon = this.data.coupons.find(c => c.id === couponId);

      if (!coupon) return;

      if (coupon.status === 'used' || coupon.status === 'expired') {
        wx.showToast({
          title: '门票已使用或过期',
          icon: 'none'
        });
        return;
      }

      try {
        const result = await wx.showModal({
          title: '确认使用',
          content: '确定要使用这张门票吗？使用后将无法撤销。'
        });

        if (result.confirm) {
          await apiService.useCoupon(couponId);
          wx.showToast({
            title: '门票使用成功',
            icon: 'success'
          });

          // 重新加载门票列表
          this.loadCoupons(true);
        }
      } catch (error) {
        console.error('使用门票失败:', error);
        wx.showToast({
          title: '使用失败',
          icon: 'none'
        });
      }
    },

    // 查看门票详情
    viewCouponDetail(e) {
      const couponId = e.currentTarget.dataset.couponId;
      const coupon = this.data.coupons.find(c => c.id === parseInt(couponId));
      if (coupon) {
        let content = `门票编码：${coupon.code}\n`;
        content += `状态：${this.getStatusText(coupon.status)}\n`;
        content += `产品：${coupon.productName}\n`;
        content += `景区：${coupon.scenicName}\n`;
        if (coupon.scenicLocation) {
          content += `位置：${coupon.scenicLocation}\n`;
        }
        if (coupon.productPrice) {
          content += `价格：¥${coupon.productPrice}\n`;
        }
        content += `创建时间：${this.formatDate(coupon.createdAt)}\n`;
        content += `有效期：${this.formatDate(coupon.validFrom)} 至 ${this.formatDate(coupon.validTo)}`;
        if (coupon.usedAt) {
          content += `\n使用时间：${this.formatDate(coupon.usedAt)}`;
        }

        wx.showModal({
          title: '门票详情',
          content: content,
          showCancel: false
        });
      }
    },

    // 查看使用规则
    viewRules() {
      wx.showModal({
        title: '卡券使用规则',
        content: '1. 门票激活后24小时内有效\n2. 每张门票仅限使用一次\n3. 门票不可转让\n4. 过期门票无法使用',
        showCancel: false,
        confirmText: '我知道了'
      });
    },

    // 跳转到景区详情页面
    goToScenicDetail(e) {
      e.stopPropagation(); // 阻止事件冒泡
      const couponId = e.currentTarget.dataset.couponId;
      const coupon = this.data.coupons.find(c => c.id === parseInt(couponId));

      if (coupon && coupon.scenicDetail && coupon.scenicDetail.scenicId) {
        wx.navigateTo({
          url: `/pages/lanhu_dulijingqu/component?scenicId=${coupon.scenicDetail.scenicId}`
        });
      } else if (coupon && coupon.productDetail && coupon.productDetail.scenicId) {
        // 如果景区详情获取失败，使用产品中的景区ID
        wx.navigateTo({
          url: `/pages/lanhu_dulijingqu/component?scenicId=${coupon.productDetail.scenicId}`
        });
      } else {
        wx.showToast({
          title: '景区信息不完整',
          icon: 'none'
        });
      }
    },

    // 跳转到产品详情页面
    goToProductDetail(e) {
      e.stopPropagation(); // 阻止事件冒泡
      const couponId = e.currentTarget.dataset.couponId;
      const coupon = this.data.coupons.find(c => c.id === parseInt(couponId));

      if (coupon && coupon.productDetail && coupon.productId) {
        wx.navigateTo({
          url: `/pages/lanhu_chanpinxiangqing/component?productId=${coupon.productId}`
        });
      } else {
        wx.showToast({
          title: '产品信息不完整',
          icon: 'none'
        });
      }
    }
});
