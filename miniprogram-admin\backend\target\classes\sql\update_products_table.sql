-- 更新产品表结构，添加订单ID外键关系
-- 执行时间：2025-01-11

-- 备份现有产品表（如果存在）
CREATE TABLE IF NOT EXISTS `products_backup` AS SELECT * FROM `products`;

-- 删除现有产品表
DROP TABLE IF EXISTS `products`;

-- 创建新的产品表结构
CREATE TABLE `products` (
  `id` int NOT NULL AUTO_INCREMENT,
  `scenic_id` int NOT NULL COMMENT '关联景区ID',
  `name` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '产品名称',
  `description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '产品描述',
  `price` decimal(10, 2) NOT NULL COMMENT '销售价格',
  `validity_hours` int NOT NULL DEFAULT 0 COMMENT '激活后有效期(小时)',
  `require_activation` tinyint(1) NOT NULL DEFAULT 1 COMMENT '是否需要激活码',
  `product_type` enum('single','bundle') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'single' COMMENT '产品类型',
  `status` tinyint NOT NULL DEFAULT 1 COMMENT '状态：1-启用，0-禁用',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `order_id` int NOT NULL COMMENT '关联订单ID',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `scenic_id`(`scenic_id` ASC) USING BTREE,
  INDEX `products_ibfk_2`(`order_id` ASC) USING BTREE,
  CONSTRAINT `products_ibfk_1` FOREIGN KEY (`scenic_id`) REFERENCES `scenics` (`id`) ON DELETE CASCADE ON UPDATE RESTRICT,
  CONSTRAINT `products_ibfk_2` FOREIGN KEY (`order_id`) REFERENCES `orders` (`id`) ON DELETE CASCADE ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci ROW_FORMAT = Dynamic;

-- 设置外键检查
SET FOREIGN_KEY_CHECKS = 1;

-- 提示信息
SELECT '产品表结构更新完成！' AS message;
SELECT '新增字段：order_id - 关联订单ID' AS field_info;
SELECT '外键约束：products_ibfk_1 (scenic_id -> scenics.id)' AS constraint_info;
SELECT '外键约束：products_ibfk_2 (order_id -> orders.id)' AS constraint_info;
