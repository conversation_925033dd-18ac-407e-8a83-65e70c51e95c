package com.tourism.miniprogram.service.impl;

import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.tourism.miniprogram.entity.Feedback;
import com.tourism.miniprogram.mapper.FeedbackMapper;
import com.tourism.miniprogram.service.FeedbackService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 意见反馈服务实现类
 *
 * <AUTHOR> Team
 * @since 2025-01-06
 */
@Slf4j
@Service
public class FeedbackServiceImpl extends ServiceImpl<FeedbackMapper, Feedback> implements FeedbackService {

    @Override
    public List<Feedback> getFeedbacksByUserId(Integer userId) {
        try {
            return baseMapper.selectFeedbacksByUserId(userId);
        } catch (Exception e) {
            log.error("根据用户ID获取反馈列表失败，userId: {}", userId, e);
            throw new RuntimeException("获取反馈列表失败");
        }
    }

    @Override
    public List<Feedback> getFeedbacksByStatus(String status) {
        try {
            return baseMapper.selectFeedbacksByStatus(status);
        } catch (Exception e) {
            log.error("根据状态获取反馈列表失败，status: {}", status, e);
            throw new RuntimeException("获取反馈列表失败");
        }
    }

    @Override
    public Integer getPendingFeedbackCount() {
        try {
            return baseMapper.countPendingFeedbacks();
        } catch (Exception e) {
            log.error("获取待处理反馈数量失败", e);
            throw new RuntimeException("获取待处理反馈数量失败");
        }
    }

    @Override
    public List<Feedback> searchFeedbacks(String keyword) {
        try {
            return baseMapper.searchFeedbacks(keyword);
        } catch (Exception e) {
            log.error("搜索反馈失败，keyword: {}", keyword, e);
            throw new RuntimeException("搜索反馈失败");
        }
    }

    @Override
    @Transactional
    public boolean replyFeedback(Integer feedbackId, String reply, Integer replyUserId) {
        try {
            UpdateWrapper<Feedback> updateWrapper = new UpdateWrapper<>();
            updateWrapper.eq("id", feedbackId)
                    .set("reply", reply)
                    .set("reply_time", LocalDateTime.now())
                    .set("reply_user_id", replyUserId)
                    .set("status", "resolved");

            boolean success = update(updateWrapper);
            if (success) {
                log.info("回复反馈成功，feedbackId: {}, replyUserId: {}", feedbackId, replyUserId);
            } else {
                log.error("回复反馈失败，feedbackId: {}", feedbackId);
            }
            return success;
        } catch (Exception e) {
            log.error("回复反馈失败，feedbackId: {}, replyUserId: {}", feedbackId, replyUserId, e);
            throw new RuntimeException("回复反馈失败");
        }
    }

    @Override
    @Transactional
    public boolean updateFeedbackStatus(Integer feedbackId, String status) {
        try {
            UpdateWrapper<Feedback> updateWrapper = new UpdateWrapper<>();
            updateWrapper.eq("id", feedbackId).set("status", status);

            boolean success = update(updateWrapper);
            if (success) {
                log.info("更新反馈状态成功，feedbackId: {}, status: {}", feedbackId, status);
            } else {
                log.error("更新反馈状态失败，feedbackId: {}", feedbackId);
            }
            return success;
        } catch (Exception e) {
            log.error("更新反馈状态失败，feedbackId: {}, status: {}", feedbackId, status, e);
            throw new RuntimeException("更新反馈状态失败");
        }
    }

    @Override
    @Transactional
    public boolean batchUpdateFeedbackStatus(List<Integer> feedbackIds, String status) {
        try {
            UpdateWrapper<Feedback> updateWrapper = new UpdateWrapper<>();
            updateWrapper.in("id", feedbackIds).set("status", status);

            boolean success = update(updateWrapper);
            if (success) {
                log.info("批量更新反馈状态成功，feedbackIds: {}, status: {}", feedbackIds, status);
            } else {
                log.error("批量更新反馈状态失败，feedbackIds: {}", feedbackIds);
            }
            return success;
        } catch (Exception e) {
            log.error("批量更新反馈状态失败，feedbackIds: {}, status: {}", feedbackIds, status, e);
            throw new RuntimeException("批量更新反馈状态失败");
        }
    }
}
